import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../constants/app_colors.dart';
import '../widgets/custom_toast.dart';
import '../api/api_provider.dart';
import '../models/collection_item.dart';
import '../models/note_item.dart';
import '../components/collection_grid/index.dart';
import '../utils/note_webview_helper.dart';
import '../utils/draft_edit_helper.dart';
import '../components/delete_confirmation_dialog.dart';

/// 笔记页面
class NotesPage extends StatefulWidget {
  const NotesPage({super.key});

  @override
  State<NotesPage> createState() => _NotesPageState();
}

class _NotesPageState extends State<NotesPage> with TickerProviderStateMixin {
  late TabController _tabController;
  final List<NoteItem> _notes = [];
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _contentController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  // API相关
  final ApiProvider _apiProvider = ApiProvider();

  // 笔记列表相关
  bool _isLoadingNotes = false;
  bool _isLoadingMoreNotes = false;
  bool _hasMoreNotes = true;
  int _currentPage = 1;
  final int _pageSize = 10;

  // 草稿相关
  final List<DraftItem> _drafts = [];
  bool _isLoadingDrafts = false;
  bool _isLoadingMoreDrafts = false;
  bool _hasMoreDrafts = true;
  int _currentDraftPage = 1;

  // 合集相关
  List<CollectionItem> _collections = [];
  bool _isLoadingCollections = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      setState(() {}); // 重新构建以更新浮动按钮显示状态
    });
    _setStatusBarColor();
    _loadNotes();
    _loadCollections();
    _setupScrollListener();

    // 延迟加载草稿，确保API初始化完成
    Future.delayed(Duration(milliseconds: 100), () {
      if (mounted) {
        _loadDrafts();
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _titleController.dispose();
    _contentController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// 设置状态栏颜色
  void _setStatusBarColor() {
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: AppColors.background,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
  }

  /// 设置滚动监听器
  void _setupScrollListener() {
    _scrollController.addListener(() {
      // 当滚动到底部附近时，加载更多数据
      if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200 &&
          !_isLoadingMoreNotes &&
          _hasMoreNotes) {
        _loadMoreNotes();
      }
    });
  }

  /// 加载笔记列表
  Future<void> _loadNotes({bool loadMore = false}) async {
    if (_isLoadingNotes || (loadMore && _isLoadingMoreNotes)) return;

    setState(() {
      if (loadMore) {
        _isLoadingMoreNotes = true;
      } else {
        _isLoadingNotes = true;
        _currentPage = 1;
      }
    });

    try {
      final response = await _apiProvider.noteApi.getNoteList(
        page: loadMore ? _currentPage + 1 : 1,
        pageSize: _pageSize,
      );

      setState(() {
        if (loadMore) {
          _notes.addAll(response.notes);
          _currentPage++;
        } else {
          _notes.clear();
          _notes.addAll(response.notes);
          _currentPage = 1;
        }

        // 检查是否还有更多数据
        _hasMoreNotes = _notes.length < response.total;
      });

      print('加载笔记成功: 当前${_notes.length}条，总共${response.total}条');
    } catch (e) {
      print('加载笔记失败: $e');
      CustomToast.show('加载笔记失败');
    } finally {
      setState(() {
        _isLoadingNotes = false;
        _isLoadingMoreNotes = false;
      });
    }
  }

  /// 加载更多笔记
  Future<void> _loadMoreNotes() async {
    await _loadNotes(loadMore: true);
  }

  /// 加载草稿列表
  Future<void> _loadDrafts({bool loadMore = false}) async {
    if (_isLoadingDrafts || (loadMore && _isLoadingMoreDrafts)) return;

    setState(() {
      if (loadMore) {
        _isLoadingMoreDrafts = true;
      } else {
        _isLoadingDrafts = true;
        _currentDraftPage = 1;
      }
    });

    try {
      // 确保API提供者已初始化
      await _apiProvider.initializeApiClient();

      final response = await _apiProvider.draftApi.getDraftList(
        page: loadMore ? _currentDraftPage + 1 : 1,
        pageSize: _pageSize,
      );

      setState(() {
        if (loadMore) {
          _drafts.addAll(response.drafts);
          _currentDraftPage++;
        } else {
          _drafts.clear();
          _drafts.addAll(response.drafts);
          _currentDraftPage = 1;
        }

        // 检查是否还有更多数据
        _hasMoreDrafts = _drafts.length < response.total;
      });

      print('加载草稿成功: 当前${_drafts.length}条，总共${response.total}条');
    } catch (e) {
      print('加载草稿失败: $e');
      CustomToast.show('加载草稿失败');
    } finally {
      setState(() {
        _isLoadingDrafts = false;
        _isLoadingMoreDrafts = false;
      });
    }
  }

  /// 加载更多草稿
  Future<void> _loadMoreDrafts() async {
    await _loadDrafts(loadMore: true);
  }

  /// 下拉刷新笔记
  Future<void> _handleNotesRefresh() async {
    await _loadNotes();
  }

  /// 下拉刷新草稿
  Future<void> _handleDraftsRefresh() async {
    await _loadDrafts();
  }

  /// 通用下拉刷新（用于兼容）
  Future<void> _handleRefresh() async {
    await _loadNotes();
    await _loadDrafts();
  }

  /// 加载合集列表
  Future<void> _loadCollections() async {
    if (_isLoadingCollections) return;

    setState(() {
      _isLoadingCollections = true;
    });

    try {
      final response = await _apiProvider.favoritesApi.getUserFavorites(
        page: 1,
        pageSize: 50,
      );

      final List<dynamic> favoritesData = response['favorites'] ?? [];
      final collections = favoritesData.map((item) => CollectionItem.fromJson(item)).toList();

      setState(() {
        _collections = collections;
      });
    } catch (e) {
      print('加载合集列表失败: $e');
      CustomToast.show('加载合集列表失败');
    } finally {
      setState(() {
        _isLoadingCollections = false;
      });
    }
  }

  /// 添加笔记
  void _addNote() {
    final title = _titleController.text.trim();
    final content = _contentController.text.trim();

    if (title.isEmpty) {
      CustomToast.show('请输入标题');
      return;
    }

    if (content.isEmpty) {
      CustomToast.show('请输入内容');
      return;
    }

    final now = DateTime.now();
    final newNote = NoteItem(
      id: now.millisecondsSinceEpoch.toString(),
      userId: '', // 临时用空字符串，实际应该从用户信息获取
      title: title,
      desc: content,
      html: '', // 临时用空字符串
      createTime: now.toIso8601String(),
      updateTime: now.toIso8601String(),
    );

    setState(() {
      _notes.insert(0, newNote);
    });

    Navigator.of(context).pop();
    CustomToast.show('笔记保存成功');
  }

  /// 删除笔记
  void _deleteNote(String id) {
    setState(() {
      _notes.removeWhere((note) => note.id == id);
    });
    CustomToast.show('笔记已删除');
  }

  /// 格式化时间
  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        if (difference.inMinutes == 0) {
          return '刚刚';
        }
        return '${difference.inMinutes}分钟前';
      }
      return '${difference.inHours}小时前';
    } else if (difference.inDays == 1) {
      return '昨天';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${dateTime.month}月${dateTime.day}日';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        automaticallyImplyLeading: false,
        toolbarHeight: 0, // 隐藏默认的toolbar
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(56.h),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.04),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: _buildCustomTabBar(),
          ),
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // 笔记页面
          _buildNotesTab(),
          // 队列页面
          _buildCollectionsTab(),
        ],
      )
    );
  }

  /// 构建自定义TabBar
  Widget _buildCustomTabBar() {
    return Container(
      height: 56.h,
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Row(
        children: [
          Expanded(
            child: _buildTabItem('笔记', 0),
          ),
          Expanded(
            child: _buildTabItem('草稿', 1),
          ),
        ],
      ),
    );
  }

  /// 构建单个Tab项
  Widget _buildTabItem(String title, int index) {
    final isSelected = _tabController.index == index;

    return GestureDetector(
      onTap: () {
        _tabController.animateTo(index);
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        height: 56.h,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 标题文字
            Text(
              title,
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                color: isSelected ? AppColors.primary : AppColors.textSecondary,
              ),
            ),
            SizedBox(height: 8.h),
            // 底部指示器
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeInOut,
              width: isSelected ? 24.w : 0,
              height: 3.h,
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(1.5.r),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建笔记Tab页面
  Widget _buildNotesTab() {
    if (_isLoadingNotes && _notes.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_notes.isEmpty) {
      return RefreshIndicator(
        onRefresh: _handleNotesRefresh,
        color: AppColors.primary,
        backgroundColor: Colors.white,
        displacement: 50.0,
        strokeWidth: 3.0,
        child: _buildEmptyState(),
      );
    }

    return _buildNotesList();
  }

  /// 构建草稿Tab页面
  Widget _buildCollectionsTab() {
    if (_isLoadingDrafts && _drafts.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_drafts.isEmpty) {
      return RefreshIndicator(
        onRefresh: _handleDraftsRefresh,
        color: AppColors.primary,
        backgroundColor: Colors.white,
        displacement: 50.0,
        strokeWidth: 3.0,
        child: _buildEmptyDraftsState(),
      );
    }

    return _buildDraftsList(_drafts);
  }

  /// 构建空的草稿状态
  Widget _buildEmptyDraftsState() {
    return CustomScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      slivers: [
        SliverFillRemaining(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.edit_note_outlined,
                  size: 80.r,
                  color: AppColors.textSecondary.withOpacity(0.5),
                ),
                SizedBox(height: 16.h),
                Text(
                  '还没有草稿',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  '创建你的第一个草稿吧',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textSecondary.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return CustomScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      slivers: [
        SliverFillRemaining(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.edit_note_outlined,
                  size: 80.r,
                  color: AppColors.textSecondary.withOpacity(0.5),
                ),
                SizedBox(height: 16.h),
                Text(
                  '还没有笔记',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  '点击右下角按钮添加第一条笔记',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textSecondary.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建笔记列表 - 大图模式
  Widget _buildNotesList() {
    return RefreshIndicator(
      onRefresh: _handleNotesRefresh,
      color: AppColors.primary,
      backgroundColor: Colors.white,
      displacement: 50.0,
      strokeWidth: 3.0,
      child: MasonryGridView.count(
        controller: _scrollController,
        padding: EdgeInsets.all(16.r),
        crossAxisCount: 2, // 每行两个
        crossAxisSpacing: 12.w,
        mainAxisSpacing: 16.h,
        physics: const AlwaysScrollableScrollPhysics(),
        itemCount: _notes.length + (_hasMoreNotes ? 1 : 0),
        itemBuilder: (context, index) {
          // 如果是最后一个位置且还有更多数据，显示加载指示器
          if (index == _notes.length) {
            return Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 20.h),
              child: Center(
                child: _isLoadingMoreNotes
                    ? SizedBox(
                        width: 24.r,
                        height: 24.r,
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                          strokeWidth: 2.w,
                        ),
                      )
                    : Text(
                        '加载更多...',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppColors.textSecondary,
                        ),
                      ),
              ),
            );
          }

          final note = _notes[index];
          return _buildNoteCard(note, index);
        },
      ),
    );
  }

  /// 构建笔记卡片
  Widget _buildNoteCard(NoteItem note, int index) {
    return Card(
      margin: EdgeInsets.zero,
      elevation: 2,
      shadowColor: AppColors.cardShadow.withOpacity(0.3),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: () async {
          // 打开笔记详情WebView
          final helper = NoteWebviewHelper();
          final success = await helper.openNoteDetail(note.id);

          if (!success) {
            CustomToast.show('打开笔记详情失败');
          }
        },
        onLongPress: () => _showNoteContextMenu(note, index),
        borderRadius: BorderRadius.circular(12.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // 封面区域 - 使用渐变背景
            _buildNoteCover(note),
            // 内容信息区域
            _buildNoteContent(note),
          ],
        ),
      ),
    );
  }

  /// 构建笔记封面
  Widget _buildNoteCover(NoteItem note) {
    return Container(
      width: double.infinity,
      height: 200.w, // 保持固定高度不变
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
        child: note.cover != null && note.cover!.isNotEmpty
            ? Image.network(
                note.cover!,
                width: double.infinity,
                height: 200.w,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  // 加载失败时显示渐变背景
                  return Container(
                    width: double.infinity,
                    height: 200.w,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          AppColors.primary.withOpacity(0.1),
                          AppColors.primaryLight.withOpacity(0.2),
                        ],
                      ),
                    ),
                    child: Icon(
                      Icons.image_not_supported,
                      size: 48.r,
                      color: AppColors.textSecondary,
                    ),
                  );
                },
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    width: double.infinity,
                    height: 200.w,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          AppColors.primary.withOpacity(0.1),
                          AppColors.primaryLight.withOpacity(0.2),
                        ],
                      ),
                    ),
                    child: Center(
                      child: CircularProgressIndicator(
                        value: loadingProgress.expectedTotalBytes != null
                            ? loadingProgress.cumulativeBytesLoaded /
                                loadingProgress.expectedTotalBytes!
                            : null,
                        color: AppColors.primary,
                      ),
                    ),
                  );
                },
              )
            : Container(
                width: double.infinity,
                height: 200.w,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.primary.withOpacity(0.1),
                      AppColors.primaryLight.withOpacity(0.2),
                    ],
                  ),
                ),
                child: Icon(
                  Icons.note,
                  size: 48.r,
                  color: AppColors.textSecondary,
                ),
              ),
      ),
    );
  }

  /// 构建笔记内容信息
  Widget _buildNoteContent(NoteItem note) {
    return Padding(
      padding: EdgeInsets.all(12.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题
          Text(
            note.title,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
              height: 1.2,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: 4.h),
          // 时间信息
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatTime(note.updatedAt ?? DateTime.now()),
                style: TextStyle(
                  fontSize: 11.sp,
                  color: AppColors.textHint,
                ),
              ),
              // 删除按钮
              GestureDetector(
                onTap: () => _showDeleteConfirmDialog(note),
                child: Icon(
                  Icons.more_horiz,
                  size: 16.r,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 显示笔记上下文菜单
  void _showNoteContextMenu(NoteItem note, int index) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r),
            topRight: Radius.circular(20.r),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 顶部指示器
            Container(
              margin: EdgeInsets.only(top: 8.h),
              width: 40.w,
              height: 4.h,
              decoration: BoxDecoration(
                color: AppColors.textHint.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            SizedBox(height: 20.h),
            // 菜单项
            ListTile(
              leading: Icon(Icons.edit, color: AppColors.primary),
              title: Text('编辑笔记'),
              onTap: () {
                Navigator.pop(context);
                CustomToast.show('编辑功能待开发');
              },
            ),
            ListTile(
              leading: Icon(Icons.delete, color: AppColors.error),
              title: Text('删除笔记'),
              onTap: () {
                Navigator.pop(context);
                _showDeleteConfirmDialog(note);
              },
            ),
            SizedBox(height: MediaQuery.of(context).padding.bottom + 16.h),
          ],
        ),
      ),
    );
  }

  /// 显示删除确认对话框
  void _showDeleteConfirmDialog(NoteItem note) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        title: Text(
          '删除笔记',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        content: Text(
          '确定要删除这条笔记吗？删除后无法恢复。',
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColors.textSecondary,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              '取消',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteNote(note.id);
            },
            child: Text(
              '删除',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.error,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建草稿列表
  Widget _buildDraftsList(List<DraftItem> drafts) {
    return RefreshIndicator(
      onRefresh: _handleDraftsRefresh,
      color: AppColors.primary,
      backgroundColor: Colors.white,
      displacement: 50.0,
      strokeWidth: 3.0,
      child: MasonryGridView.count(
        controller: _scrollController,
        padding: EdgeInsets.all(16.r),
        crossAxisCount: 2, // 每行两个
        crossAxisSpacing: 12.w,
        mainAxisSpacing: 16.h,
        physics: const AlwaysScrollableScrollPhysics(),
        itemCount: drafts.length + (_hasMoreDrafts ? 1 : 0),
        itemBuilder: (context, index) {
          // 如果是最后一个位置且还有更多数据，显示加载指示器
          if (index == drafts.length) {
            return Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 20.h),
              child: Center(
                child: _isLoadingMoreDrafts
                    ? SizedBox(
                        width: 24.r,
                        height: 24.r,
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                          strokeWidth: 2.w,
                        ),
                      )
                    : GestureDetector(
                        onTap: _loadMoreDrafts,
                        child: Text(
                          '加载更多...',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ),
              ),
            );
          }

          final draft = drafts[index];
          return _buildDraftCard(draft, index);
        },
      ),
    );
  }

  /// 构建草稿卡片
  Widget _buildDraftCard(DraftItem draft, int index) {
    return Card(
      margin: EdgeInsets.zero,
      elevation: 2,
      shadowColor: AppColors.cardShadow.withOpacity(0.3),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: () => _openDraftEdit(draft.noteId),
        onLongPress: () => _showDraftContextMenu(draft, index),
        borderRadius: BorderRadius.circular(12.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // 封面区域 - 使用渐变背景
            _buildDraftCover(draft),
            // 内容信息区域
            _buildDraftContent(draft),
          ],
        ),
      ),
    );
  }

  /// 构建草稿封面
  Widget _buildDraftCover(DraftItem draft) {
    return Container(
      width: double.infinity,
      height: 200.w, // 保持固定高度不变
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
        child: draft.noteCover != null && draft.noteCover!.isNotEmpty
            ? Image.network(
                draft.noteCover!,
                width: double.infinity,
                height: 200.w,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  // 加载失败时显示渐变背景
                  return Container(
                    width: double.infinity,
                    height: 200.w,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          AppColors.primary.withOpacity(0.1),
                          AppColors.primaryLight.withOpacity(0.2),
                        ],
                      ),
                    ),
                    child: Icon(
                      Icons.edit_note,
                      size: 48.r,
                      color: AppColors.textSecondary,
                    ),
                  );
                },
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    width: double.infinity,
                    height: 200.w,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          AppColors.primary.withOpacity(0.1),
                          AppColors.primaryLight.withOpacity(0.2),
                        ],
                      ),
                    ),
                    child: Center(
                      child: CircularProgressIndicator(
                        value: loadingProgress.expectedTotalBytes != null
                            ? loadingProgress.cumulativeBytesLoaded /
                                loadingProgress.expectedTotalBytes!
                            : null,
                        color: AppColors.primary,
                      ),
                    ),
                  );
                },
              )
            : Container(
                width: double.infinity,
                height: 200.w,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.primary.withOpacity(0.1),
                      AppColors.primaryLight.withOpacity(0.2),
                    ],
                  ),
                ),
                child: Icon(
                  Icons.edit_note,
                  size: 48.r,
                  color: AppColors.textSecondary,
                ),
              ),
      ),
    );
  }

  /// 构建草稿内容信息
  Widget _buildDraftContent(DraftItem draft) {
    return Padding(
      padding: EdgeInsets.all(12.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题
          Text(
            draft.noteTitle,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
              height: 1.2,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: 4.h),
          // 时间信息和草稿标识
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatTime(draft.updatedAt ?? DateTime.now()),
                style: TextStyle(
                  fontSize: 11.sp,
                  color: AppColors.textHint,
                ),
              ),
              // 草稿标识
              Container(
                padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Text(
                  '草稿',
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: AppColors.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 显示草稿上下文菜单
  void _showDraftContextMenu(DraftItem draft, int index) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.symmetric(vertical: 20.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 删除草稿
            ListTile(
              leading: Icon(Icons.delete, color: AppColors.error),
              title: Text(
                '删除草稿',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: AppColors.error,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                _showDeleteDraftConfirmDialog(draft);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 显示删除草稿确认对话框
  Future<void> _showDeleteDraftConfirmDialog(DraftItem draft) async {
    final confirmed = await DeleteConfirmationDialog.show(
      context,
      title: '删除草稿',
      content: '确定要删除这个草稿吗？删除后无法恢复。',
    );

    if (confirmed) {
      _deleteDraft(draft);
    }
  }

  /// 跳转到编辑草稿页面
  Future<void> _openDraftEdit(String noteId) async {
    try {
      final helper = DraftEditHelper();
      final success = await helper.openDraftEdit(context, noteId);

      if (!success) {
        // 如果没有草稿内容，可以选择打开笔记详情
        final noteHelper = NoteWebviewHelper();
        final noteSuccess = await noteHelper.openNoteDetail(noteId);

        if (!noteSuccess) {
          CustomToast.show('打开笔记详情失败');
        }
      }
    } catch (e) {
      print('编辑草稿失败: $e');
      CustomToast.show('编辑草稿失败');
    }
  }

  /// 编辑草稿
  void _editDraft(DraftItem draft) {
    // TODO: 实现编辑草稿功能
    CustomToast.show('编辑草稿功能待实现');
  }

  /// 发布草稿为笔记
  Future<void> _publishDraft(DraftItem draft) async {
    try {
      await _apiProvider.draftApi.publishDraft(draft.id);
      CustomToast.show('草稿发布成功');
      // 刷新草稿列表和笔记列表
      await _loadDrafts();
      await _loadNotes();
    } catch (e) {
      print('发布草稿失败: $e');
      CustomToast.show('发布草稿失败');
    }
  }

  /// 删除草稿
  Future<void> _deleteDraft(DraftItem draft) async {
    try {
      await _apiProvider.draftApi.deleteDraftByNoteId(draft.noteId);
      CustomToast.show('草稿删除成功');
      // 从列表中移除并刷新
      setState(() {
        _drafts.removeWhere((item) => item.id == draft.id);
      });
    } catch (e) {
      print('删除草稿失败: $e');
      CustomToast.show('删除草稿失败');
    }
  }
}


